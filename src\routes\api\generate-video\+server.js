import { json, error } from '@sveltejs/kit';
import OpenAI from 'openai';
import { OPENAI_API_KEY } from '$env/static/private';

const openai = new OpenAI({
	apiKey: OPENAI_API_KEY
});

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
	try {
		const { script, voice = 'alloy', speed = 1.0 } = await request.json();

		if (!script || script.trim().length === 0) {
			throw error(400, 'Script is required');
		}

		if (script.length > 4096) {
			throw error(400, 'Script too long. Maximum 4096 characters for 30s video.');
		}

		console.log('Starting video generation pipeline...');
		console.log('Script length:', script.length, 'characters');

		// Step 1: Generate TTS Audio
		console.log('Step 1: Generating TTS audio...');
		const mp3Response = await openai.audio.speech.create({
			model: 'tts-1',
			voice: voice,
			input: script,
			speed: speed
		});

		const audioBuffer = Buffer.from(await mp3Response.arrayBuffer());
		console.log('TTS completed. Audio size:', audioBuffer.length, 'bytes');

		// Step 2: Create audio file for Whisper
		const audioFile = new File([audioBuffer], 'speech.mp3', { type: 'audio/mpeg' });

		// Step 3: Get captions from Whisper
		console.log('Step 2: Transcribing with Whisper...');
		const transcription = await openai.audio.transcriptions.create({
			file: audioFile,
			model: 'whisper-1',
			response_format: 'verbose_json',
			timestamp_granularities: ['segment']
		});

		console.log('Whisper completed. Segments:', transcription.segments?.length || 0);

		// Step 4: Convert audio to base64 for frontend
		const audioBase64 = audioBuffer.toString('base64');
		const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;

		return json({
			success: true,
			audio: {
				url: audioDataUrl,
				duration: transcription.duration || 0,
				size: audioBuffer.length
			},
			captions: {
				text: transcription.text,
				segments: transcription.segments || []
			},
			metadata: {
				script: script,
				voice: voice,
				speed: speed,
				generatedAt: new Date().toISOString()
			}
		});

	} catch (err) {
		console.error('Video Generation Error:', err);
		
		if (err.status) {
			throw error(err.status, err.message);
		}
		
		throw error(500, 'Failed to generate video');
	}
}
