import { json, error } from '@sveltejs/kit';
import OpenAI from 'openai';
import { OPENAI_API_KEY } from '$env/static/private';

const openai = new OpenAI({
	apiKey: OPENAI_API_KEY
});

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
	try {
		const { script, voice = 'alloy', speed = 1.0, visualPrompt, visualMode = 'image' } = await request.json();

		if (!script || script.trim().length === 0) {
			throw error(400, 'Script is required');
		}

		if (!visualPrompt || visualPrompt.trim().length === 0) {
			throw error(400, 'Visual prompt is required');
		}

		if (script.length > 500) {
			throw error(400, 'Script too long. Maximum 500 characters for 30s video.');
		}

		if (visualPrompt.length > 500) {
			throw error(400, 'Visual prompt too long. Maximum 500 characters.');
		}

		console.log('Starting parallel video generation pipeline...');
		console.log('Script length:', script.length, 'characters');
		console.log('Visual prompt length:', visualPrompt.length, 'characters');
		console.log('Visual mode:', visualMode);

		// Run audio generation and image generation in parallel
		console.log('Starting parallel generation: Audio + Image...');

		const [audioResult, imageResult] = await Promise.all([
			// Audio Generation Pipeline
			(async () => {
				console.log('Generating TTS audio...');
				const mp3Response = await openai.audio.speech.create({
					model: 'tts-1',
					voice: voice,
					input: script,
					speed: speed
				});

				const audioBuffer = Buffer.from(await mp3Response.arrayBuffer());
				console.log('TTS completed. Audio size:', audioBuffer.length, 'bytes');

				// Create audio file for Whisper
				const audioFile = new File([audioBuffer], 'speech.mp3', { type: 'audio/mpeg' });

				// Get captions from Whisper
				console.log('Transcribing with Whisper...');
				const transcription = await openai.audio.transcriptions.create({
					file: audioFile,
					model: 'whisper-1',
					response_format: 'verbose_json',
					timestamp_granularities: ['segment']
				});

				console.log('Whisper completed. Segments:', transcription.segments?.length || 0);

				return {
					audioBuffer,
					transcription
				};
			})(),

			// Image Generation Pipeline
			(async () => {
				console.log('Generating image with DALL-E...');
				const imageResponse = await openai.images.generate({
					model: 'dall-e-3',
					prompt: visualPrompt,
					size: '1024x1792', // Vertical aspect ratio for YouTube Shorts
					quality: 'standard',
					n: 1
				});

				console.log('DALL-E completed. Image URL:', imageResponse.data[0].url);

				// Download the image and convert to base64
				const imageUrl = imageResponse.data[0].url;
				const imageResponse2 = await fetch(imageUrl);
				const imageBuffer = Buffer.from(await imageResponse2.arrayBuffer());
				const imageBase64 = imageBuffer.toString('base64');
				const imageDataUrl = `data:image/png;base64,${imageBase64}`;

				console.log('Image downloaded and converted. Size:', imageBuffer.length, 'bytes');

				return {
					imageUrl: imageDataUrl,
					originalUrl: imageUrl,
					size: imageBuffer.length
				};
			})()
		]);

		console.log('Parallel generation completed!');

		// Convert audio to base64 for frontend
		const audioBase64 = audioResult.audioBuffer.toString('base64');
		const audioDataUrl = `data:audio/mpeg;base64,${audioBase64}`;

		return json({
			success: true,
			audio: {
				url: audioDataUrl,
				duration: audioResult.transcription.duration || 0,
				size: audioResult.audioBuffer.length
			},
			captions: {
				text: audioResult.transcription.text,
				segments: audioResult.transcription.segments || []
			},
			image: {
				url: imageResult.imageUrl,
				originalUrl: imageResult.originalUrl,
				size: imageResult.size
			},
			metadata: {
				script: script,
				voice: voice,
				speed: speed,
				visualPrompt: visualPrompt,
				visualMode: visualMode,
				generatedAt: new Date().toISOString()
			}
		});

	} catch (err) {
		console.error('Video Generation Error:', err);
		
		if (err.status) {
			throw error(err.status, err.message);
		}
		
		throw error(500, 'Failed to generate video');
	}
}
