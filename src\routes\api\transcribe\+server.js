import { json, error } from '@sveltejs/kit';
import OpenAI from 'openai';
import { OPENAI_API_KEY } from '$env/static/private';

const openai = new OpenAI({
	apiKey: OPENAI_API_KEY
});

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
	try {
		const formData = await request.formData();
		const audioFile = formData.get('audio');

		if (!audioFile || !(audioFile instanceof File)) {
			throw error(400, 'Audio file is required');
		}

		// Check file size (max 25MB for Whisper API)
		if (audioFile.size > 25 * 1024 * 1024) {
			throw error(400, 'Audio file too large. Maximum 25MB.');
		}

		console.log('Transcribing audio file:', audioFile.name, 'Size:', audioFile.size);

		const transcription = await openai.audio.transcriptions.create({
			file: audioFile,
			model: 'whisper-1',
			response_format: 'verbose_json',
			timestamp_granularities: ['segment']
		});

		console.log('Transcription completed. Segments:', transcription.segments?.length || 0);

		return json({
			text: transcription.text,
			segments: transcription.segments || [],
			duration: transcription.duration || 0
		});
	} catch (err) {
		console.error('Transcription Error:', err);
		
		if (err.status) {
			throw error(err.status, err.message);
		}
		
		throw error(500, 'Failed to transcribe audio');
	}
}
