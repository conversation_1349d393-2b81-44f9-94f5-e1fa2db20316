import { json, error } from '@sveltejs/kit';
import OpenAI from 'openai';
import { OPENAI_API_KEY } from '$env/static/private';

const openai = new OpenAI({
	apiKey: OPENAI_API_KEY
});

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
	try {
		const { text, voice = 'alloy', speed = 1.0 } = await request.json();

		if (!text || text.trim().length === 0) {
			throw error(400, 'Text is required');
		}

		if (text.length > 4096) {
			throw error(400, 'Text too long. Maximum 4096 characters.');
		}

		console.log('Generating TTS for text:', text.substring(0, 50) + '...');

		const mp3 = await openai.audio.speech.create({
			model: 'tts-1',
			voice: voice, // alloy, echo, fable, onyx, nova, shimmer
			input: text,
			speed: speed
		});

		const buffer = Buffer.from(await mp3.arrayBuffer());

		return new Response(buffer, {
			headers: {
				'Content-Type': 'audio/mpeg',
				'Content-Length': buffer.length.toString(),
				'Cache-Control': 'public, max-age=3600'
			}
		});
	} catch (err) {
		console.error('TTS Error:', err);
		
		if (err.status) {
			throw error(err.status, err.message);
		}
		
		throw error(500, 'Failed to generate speech');
	}
}
