<script lang="ts">
	import { getContext } from 'svelte';

	interface CaptionSegment {
		start: number;
		end: number;
		text: string;
	}

	// Get dark mode context from layout
	const darkModeContext = getContext<{value: boolean, toggle: () => void}>('darkMode');
	const darkMode = $derived(darkModeContext?.value ?? false);

	let script = $state('');
	let isGenerating = $state(false);
	let generationStep = $state('');
	let error = $state('');

	// Video data
	let audioUrl = $state('');
	let captions = $state<CaptionSegment[]>([]);
	let currentCaption = $state('');
	let audioDuration = $state(0);
	let imageUrl = $state('');

	// Video canvas and rendering
	let videoCanvas = $state<HTMLCanvasElement>();
	let videoContext: CanvasRenderingContext2D;
	let backgroundImage: HTMLImageElement | null = null;
	let isRecording = $state(false);
	let videoBlob = $state<Blob | null>(null);
	let videoUrl = $state('');
	
	// Caption styling
	let captionStyle = $state({
		fontSize: 18,
		color: '#EE8C36',
		backgroundColor: 'rgba(0, 0, 0, 0.8)',
		fontFamily: 'Arial',
		position: 'bottom',
		padding: 12
	});
	
	// Voice options
	let selectedVoice = $state('alloy');
	let speechSpeed = $state(1.0);

	// Visual content generation
	let visualPrompt = $state('');
	let visualMode = $state<'image' | 'video'>('image');
	
	const voices = [
		{ value: 'alloy', label: 'Alloy (Neutral)' },
		{ value: 'echo', label: 'Echo (Male)' },
		{ value: 'fable', label: 'Fable (British Male)' },
		{ value: 'onyx', label: 'Onyx (Deep Male)' },
		{ value: 'nova', label: 'Nova (Female)' },
		{ value: 'shimmer', label: 'Shimmer (Soft Female)' }
	];
	
	async function generateVideo() {
		if (!script.trim()) {
			error = 'Please enter a script';
			return;
		}

		if (!visualPrompt.trim()) {
			error = 'Please enter a visual prompt';
			return;
		}

		isGenerating = true;
		error = '';
		audioUrl = '';
		imageUrl = '';
		captions = [];
		currentCaption = '';

		// Reset canvas context for new generation
		videoContext = null as any;
		console.log('Starting new generation - canvas will re-initialize');

		try {
			generationStep = `Generating ${visualMode}, audio, and captions in parallel...`;

			const response = await fetch('/api/generate-video', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({
					script: script.trim(),
					voice: selectedVoice,
					speed: speechSpeed,
					visualPrompt: visualPrompt.trim(),
					visualMode: visualMode
				})
			});
			
			if (!response.ok) {
				const errorData = await response.json();
				throw new Error(errorData.message || 'Generation failed');
			}
			
			generationStep = 'Processing results...';
			const result = await response.json();

			// Set the generated data
			audioUrl = result.audio.url;
			imageUrl = result.image.url;
			captions = result.captions.segments;
			audioDuration = result.audio.duration;

			// Load the background image
			if (imageUrl) {
				console.log('Loading background image...');
				backgroundImage = new Image();
				backgroundImage.onload = () => {
					console.log('Background image loaded successfully');
					// Force canvas re-initialization for new video
					if (videoCanvas) {
						console.log('Re-initializing canvas for new video...');
						initVideoCanvas();
					}
				};
				backgroundImage.onerror = (e) => {
					console.error('Failed to load background image:', e);
					backgroundImage = null;
					// Still initialize canvas even if image fails
					if (videoCanvas) {
						console.log('Re-initializing canvas without background image...');
						initVideoCanvas();
					}
				};
				backgroundImage.src = imageUrl;
			} else {
				// Force canvas re-initialization for new video
				if (videoCanvas) {
					console.log('Re-initializing canvas for new video...');
					initVideoCanvas();
				}
			}

			generationStep = 'Complete!';
			console.log('Video generation completed:', result);
			console.log('Audio URL length:', audioUrl?.length);
			console.log('Audio URL starts with:', audioUrl?.substring(0, 50));
			
		} catch (err) {
			console.error('Generation error:', err);
			error = (err instanceof Error ? err.message : String(err)) || 'Failed to generate video';
		} finally {
			isGenerating = false;
			generationStep = '';
		}
	}

	function updateCaption(currentTime: number) {
		const active = captions.find(cap =>
			currentTime >= cap.start && currentTime <= cap.end
		);

		// If there's an active caption, use it
		if (active) {
			const newCaption = active.text;
			if (newCaption !== currentCaption) {
				currentCaption = newCaption;
				console.log('Caption updated:', currentCaption, 'at time:', currentTime.toFixed(1));
			}
		}
		// If no active caption but we're past all captions, keep the last one
		else if (currentTime > 0 && captions.length > 0) {
			const lastCaption = captions[captions.length - 1];
			if (currentTime >= lastCaption.end && currentCaption !== lastCaption.text) {
				currentCaption = lastCaption.text;
				console.log('Keeping last caption:', currentCaption, 'at time:', currentTime.toFixed(1));
			}
		}
	}

	// Initialize canvas when it's available and ensure it's ready
	$effect(() => {
		if (videoCanvas && !videoContext) {
			// Longer delay to ensure DOM is fully ready
			setTimeout(() => {
				if (videoCanvas && !videoContext) {
					console.log('Auto-initializing canvas...');
					initVideoCanvas();
				}
			}, 500); // Increased delay
		}
	});

	// Also initialize when audio is generated
	$effect(() => {
		if (audioUrl && videoCanvas && !videoContext) {
			console.log('Initializing canvas after audio generation...');
			initVideoCanvas();
		}
	});

	// Update video canvas when caption changes (only if canvas is ready)
	$effect(() => {
		if (videoCanvas && videoContext && currentCaption !== undefined) {
			drawVideoFrame(currentCaption);
		}
	});
	
	function resetGeneration() {
		audioUrl = '';
		imageUrl = '';
		backgroundImage = null;
		captions = [];
		currentCaption = '';
		error = '';
		audioDuration = 0;
		videoUrl = '';
		videoBlob = null;

		// Reset canvas context so it gets re-initialized
		videoContext = null as any;

		// Clear canvas if it exists
		if (videoCanvas && videoContext) {
			videoContext.clearRect(0, 0, videoCanvas.width, videoCanvas.height);
		}

		console.log('Generation reset - canvas will re-initialize');
	}

	// Initialize video canvas
	function initVideoCanvas() {
		if (!videoCanvas) {
			console.log('Canvas not available for initialization');
			return;
		}

		console.log('Initializing video canvas...');
		videoContext = videoCanvas.getContext('2d')!;

		// Set canvas size for YouTube Shorts (1080x1920)
		const width = 1080;
		const height = 1920;

		videoCanvas.width = width;
		videoCanvas.height = height;

		console.log('Canvas initialized:', { width, height });

		// Draw initial frame with visible test elements
		videoContext.fillStyle = '#000000';
		videoContext.fillRect(0, 0, width, height);

		// Draw a visible border to confirm canvas is working
		videoContext.strokeStyle = '#EE8C36';
		videoContext.lineWidth = 10;
		videoContext.strokeRect(5, 5, width - 10, height - 10);

		// Draw initial test text
		videoContext.fillStyle = '#EE8C36';
		videoContext.font = 'bold 80px Arial';
		videoContext.textAlign = 'center';
		videoContext.fillText('Canvas Ready', width / 2, height / 2);
		videoContext.fillText('Generate video to see captions', width / 2, height / 2 + 100);
	}

	// Draw a single video frame with caption
	function drawVideoFrame(caption: string) {
		if (!videoContext || !videoCanvas) {
			return;
		}

		const width = videoCanvas.width;
		const height = videoCanvas.height;

		// Clear and fill with black background
		videoContext.fillStyle = '#000000';
		videoContext.fillRect(0, 0, width, height);

		// Draw background image if available
		if (backgroundImage && backgroundImage.complete) {
			// Calculate scaling to fit the canvas while maintaining aspect ratio
			const imageAspect = backgroundImage.width / backgroundImage.height;
			const canvasAspect = width / height;

			let drawWidth, drawHeight, drawX, drawY;

			if (imageAspect > canvasAspect) {
				// Image is wider than canvas - fit to height
				drawHeight = height;
				drawWidth = height * imageAspect;
				drawX = (width - drawWidth) / 2;
				drawY = 0;
			} else {
				// Image is taller than canvas - fit to width
				drawWidth = width;
				drawHeight = width / imageAspect;
				drawX = 0;
				drawY = (height - drawHeight) / 2;
			}

			videoContext.drawImage(backgroundImage, drawX, drawY, drawWidth, drawHeight);
		}

		// Draw caption if provided
		if (caption && caption.trim()) {
			// Scale font size for 1080p resolution
			const scaledFontSize = Math.max(captionStyle.fontSize * 4, 60); // Ensure minimum readable size
			const scaledPadding = captionStyle.padding * 4;

			const captionY = height - 300; // Position from bottom (more space for 1080p)
			const maxWidth = width - 200; // Padding from sides

			// Set font for measuring
			videoContext.font = `bold ${scaledFontSize}px ${captionStyle.fontFamily}`;

			// Word wrap for long captions
			const words = caption.split(' ');
			const lines: string[] = [];
			let currentLine = '';

			for (const word of words) {
				const testLine = currentLine + (currentLine ? ' ' : '') + word;
				const testWidth = videoContext.measureText(testLine).width;

				if (testWidth > maxWidth && currentLine) {
					lines.push(currentLine);
					currentLine = word;
				} else {
					currentLine = testLine;
				}
			}
			if (currentLine) lines.push(currentLine);

			// Calculate total text height
			const lineHeight = scaledFontSize * 1.3;
			const totalTextHeight = lines.length * lineHeight;
			const startY = captionY - totalTextHeight / 2;

			// Draw background for all lines
			const maxLineWidth = Math.max(...lines.map(line => videoContext.measureText(line).width));
			const bgWidth = maxLineWidth + (scaledPadding * 2);
			const bgHeight = totalTextHeight + (scaledPadding * 2);
			const bgX = (width - bgWidth) / 2;
			const bgY = startY - scaledPadding;

			// Draw semi-transparent background
			videoContext.fillStyle = captionStyle.backgroundColor;
			videoContext.fillRect(bgX, bgY, bgWidth, bgHeight);

			// Draw text lines
			videoContext.fillStyle = captionStyle.color;
			videoContext.textAlign = 'center';
			videoContext.textBaseline = 'top';

			lines.forEach((line, index) => {
				const lineY = startY + (index * lineHeight);
				videoContext.fillText(line, width / 2, lineY);
			});
		}
	}

	// Export video with audio and captions
	async function exportVideo() {
		if (!videoCanvas || !audioUrl || !captions.length) {
			console.error('Missing required data for video export');
			return;
		}

		isRecording = true;

		try {
			console.log('Starting video export...');

			// Create MediaRecorder for canvas
			const canvasStream = videoCanvas.captureStream(30); // 30 FPS

			// Create audio context and source
			const audioElement = new Audio(audioUrl);
			const audioContext = new AudioContext();
			const audioSource = audioContext.createMediaElementSource(audioElement);
			const audioDestination = audioContext.createMediaStreamDestination();
			audioSource.connect(audioDestination);
			audioSource.connect(audioContext.destination);

			// Combine video and audio streams
			const combinedStream = new MediaStream([
				...canvasStream.getVideoTracks(),
				...audioDestination.stream.getAudioTracks()
			]);

			// Try different video formats for better compatibility
			let mimeType = 'video/webm;codecs=vp9,opus';
			if (!MediaRecorder.isTypeSupported(mimeType)) {
				mimeType = 'video/webm;codecs=vp8,opus';
				if (!MediaRecorder.isTypeSupported(mimeType)) {
					mimeType = 'video/webm';
				}
			}

			console.log('Using MIME type:', mimeType);

			// Set up MediaRecorder
			const mediaRecorder = new MediaRecorder(combinedStream, {
				mimeType: mimeType,
				videoBitsPerSecond: 2500000, // 2.5 Mbps for good quality
				audioBitsPerSecond: 128000   // 128 kbps for audio
			});

			const chunks: Blob[] = [];

			mediaRecorder.ondataavailable = (event) => {
				if (event.data.size > 0) {
					chunks.push(event.data);
				}
			};

			mediaRecorder.onstop = () => {
				videoBlob = new Blob(chunks, { type: mimeType });
				videoUrl = URL.createObjectURL(videoBlob);
				isRecording = false;
				console.log('Video export completed!', 'Size:', videoBlob.size, 'bytes');
			};

			// Start recording
			mediaRecorder.start();

			// Play audio and animate captions
			audioElement.currentTime = 0;
			await audioElement.play();

			// Update captions during playback
			const updateInterval = setInterval(() => {
				if (audioElement.ended) {
					clearInterval(updateInterval);
					mediaRecorder.stop();
					canvasStream.getTracks().forEach(track => track.stop());
					audioContext.close();
					return;
				}

				updateCaption(audioElement.currentTime);
				drawVideoFrame(currentCaption);
			}, 1000 / 30); // 30 FPS updates

		} catch (error) {
			console.error('Video export failed:', error);
			isRecording = false;
		}
	}
</script>

<div class="container mx-auto max-w-7xl p-6">
	<h1 class="text-3xl font-bold text-[#EE8C36] mb-8">AI Video Generator</h1>

	<!-- Main Layout: Controls Left, Video Right -->
	<div class="grid grid-cols-1 xl:grid-cols-2 gap-8">

		<!-- Left Column: ALL Controls -->
		<div class="space-y-6">

			<!-- Script Input -->
			<div>
				<label for="script" class="block text-sm font-medium mb-2">Video Script</label>
				<textarea
					id="script"
					bind:value={script}
					placeholder="Enter your script here... (max 500 characters for 30s video)"
					class={"w-full h-32 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-[#EE8C36] focus:border-transparent " + (darkMode ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900')}
					maxlength="500"
				></textarea>
				<div class="text-sm text-gray-500 mt-1">
					{script.length}/500 characters
				</div>
			</div>

			<!-- Visual Content Prompt -->
			<div>
				<div class="flex items-center justify-between mb-2">
					<label for="visual-prompt" class="block text-sm font-medium">
						Describe {visualMode} or image prompt for generation
					</label>

					<!-- Toggle between Image and Video -->
					<div class="flex items-center space-x-2">
						<span class={"text-sm " + (visualMode === 'image' ? 'text-[#EE8C36] font-medium' : 'text-gray-500')}>
							Image
						</span>
						<button
							onclick={() => visualMode = visualMode === 'image' ? 'video' : 'image'}
							aria-label="Toggle between image and video mode"
							class={"relative inline-flex h-6 w-11 items-center rounded-full transition-colors " + (visualMode === 'video' ? 'bg-[#EE8C36]' : 'bg-gray-300')}
						>
							<span class={"inline-block h-4 w-4 transform rounded-full bg-white transition-transform " + (visualMode === 'video' ? 'translate-x-6' : 'translate-x-1')}></span>
						</button>
						<span class={"text-sm " + (visualMode === 'video' ? 'text-[#EE8C36] font-medium' : 'text-gray-500')}>
							Video
						</span>
					</div>
				</div>

				<textarea
					id="visual-prompt"
					bind:value={visualPrompt}
					placeholder={visualMode === 'image'
						? "Describe the image you want to generate (e.g., 'A serene mountain landscape at sunset with golden light')"
						: "Describe the video scene you want to generate (e.g., 'A time-lapse of clouds moving over a city skyline')"}
					class={"w-full h-32 p-3 border rounded-lg resize-none focus:ring-2 focus:ring-[#EE8C36] focus:border-transparent " + (darkMode ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400' : 'bg-white border-gray-300 text-gray-900')}
					maxlength="500"
				></textarea>
				<div class="text-sm text-gray-500 mt-1">
					{visualPrompt.length}/500 characters • {visualMode === 'image' ? '🖼️' : '🎬'} {visualMode.charAt(0).toUpperCase() + visualMode.slice(1)} mode
				</div>
			</div>

			<!-- Voice & Speed Controls -->
			<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<div>
					<label for="voice" class="block text-sm font-medium mb-2">Voice</label>
					<select
						id="voice"
						bind:value={selectedVoice}
						class={"w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36] " + (darkMode ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900')}
					>
						{#each voices as voice}
							<option value={voice.value}>{voice.label}</option>
						{/each}
					</select>
				</div>

				<div>
					<label for="speed" class="block text-sm font-medium mb-2">
						Speech Speed: {speechSpeed}x
					</label>
					<input
						id="speed"
						type="range"
						bind:value={speechSpeed}
						min="0.25"
						max="4.0"
						step="0.25"
						class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
					/>
				</div>
			</div>

			<!-- Generate Button -->
			<div>
				<button
					onclick={generateVideo}
					disabled={isGenerating || !script.trim() || !visualPrompt.trim()}
					class="w-full bg-[#EE8C36] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#d67a2e] disabled:opacity-50 disabled:cursor-not-allowed"
				>
					{isGenerating ? 'Generating...' : 'Generate Video'}
				</button>

				{#if audioUrl}
					<button
						onclick={resetGeneration}
						class="w-full mt-2 bg-gray-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-600"
					>
						Reset
					</button>
				{/if}
			</div>
	
			<!-- Generation Status -->
			{#if isGenerating && generationStep}
				<div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
					<div class="flex items-center">
						<div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-3"></div>
						<span class="text-blue-800">{generationStep}</span>
					</div>
				</div>
			{/if}

			<!-- Error Display -->
			{#if error}
				<div class="p-4 bg-red-50 border border-red-200 rounded-lg">
					<p class="text-red-800">{error}</p>
				</div>
			{/if}
	
			<!-- Audio Controls (when generated) -->
			{#if audioUrl}
				<div>
					<h3 class="text-lg font-medium mb-2">Audio Controls</h3>
					<audio
						src={audioUrl}
						controls
						class="w-full"
						style="height: 54px; z-index: 10; position: relative;"
						ontimeupdate={e => {
							const target = e.target as HTMLAudioElement;
							if (target) {
								updateCaption(target.currentTime);
								// Only draw if canvas is ready
								if (videoContext) {
									drawVideoFrame(currentCaption);
								}
							}
						}}
						onloadstart={() => console.log('Audio loading started')}
						oncanplay={() => console.log('Audio can play')}
						onerror={(e) => console.error('Audio error:', e)}
						onloadeddata={() => console.log('Audio data loaded')}
					>
						Your browser does not support the audio element.
					</audio>
				</div>

				<!-- Audio Info -->
				<div class="text-sm text-gray-600">
					<p>Duration: {audioDuration.toFixed(1)}s | Segments: {captions.length}</p>
					<p>Audio URL: {audioUrl ? 'Generated ✓' : 'Not available'}</p>
					{#if currentCaption}
						<p class="mt-1">Current: "{currentCaption}"</p>
					{/if}
				</div>

				<!-- Action Buttons -->
				<div class="space-y-2">

					<button
						onclick={() => {
							if (audioUrl) {
								const audio = new Audio(audioUrl);
								audio.play().catch(e => console.error('Audio play failed:', e));
								console.log('Attempting to play audio directly...');
							}
						}}
						class="w-full bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
					>
						🔊 Test Audio Playback
					</button>

					<button
						onclick={exportVideo}
						disabled={isRecording || !audioUrl}
						class="w-full bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
					>
						{isRecording ? '🎬 Recording...' : '📹 Export Video'}
					</button>

					{#if videoUrl}
						<a
							href={videoUrl}
							download="ai-video.webm"
							class="block w-full bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 text-center"
						>
							⬇️ Download Video (.webm)
						</a>
					{/if}
				</div>
			{/if}

			<!-- Caption Style Controls -->
			{#if audioUrl}
				<div>
					<h3 class="text-lg font-semibold mb-4">Caption Styling</h3>

					<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
				<!-- Font Size -->
				<div>
					<label for="font-size-slider" class="block text-sm font-medium mb-2">
						Font Size: {captionStyle.fontSize}px
					</label>
					<input
						id="font-size-slider"
						type="range"
						bind:value={captionStyle.fontSize}
						min="12"
						max="32"
						step="1"
						class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
					/>
				</div>

				<!-- Text Color -->
				<div>
					<label for="text-color-input" class="block text-sm font-medium mb-2">Text Color</label>
					<input
						id="text-color-input"
						type="color"
						bind:value={captionStyle.color}
						class={"w-full h-10 rounded border " + (darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300')}
					/>
				</div>

				<!-- Background Color -->
				<div>
					<label for="background-color-input" class="block text-sm font-medium mb-2">Background</label>
					<input
						id="background-color-input"
						type="color"
						bind:value={captionStyle.backgroundColor}
						class={"w-full h-10 rounded border " + (darkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-300')}
					/>
				</div>

				<!-- Font Family -->
				<div>
					<label for="font-family-select" class="block text-sm font-medium mb-2">Font Family</label>
					<select
						id="font-family-select"
						bind:value={captionStyle.fontFamily}
						class={"w-full p-2 border rounded-lg focus:ring-2 focus:ring-[#EE8C36] " + (darkMode ? 'bg-gray-800 border-gray-600 text-white' : 'bg-white border-gray-300 text-gray-900')}
					>
						<option value="Arial">Arial</option>
						<option value="Helvetica">Helvetica</option>
						<option value="Times New Roman">Times New Roman</option>
						<option value="Georgia">Georgia</option>
						<option value="Verdana">Verdana</option>
						<option value="Courier New">Courier New</option>
					</select>
				</div>

				<!-- Padding -->
				<div>
					<label for="padding-slider" class="block text-sm font-medium mb-2">
						Padding: {captionStyle.padding}px
					</label>
					<input
						id="padding-slider"
						type="range"
						bind:value={captionStyle.padding}
						min="4"
						max="24"
						step="2"
						class={"w-full " + (darkMode ? 'accent-[#EE8C36]' : '')}
					/>
				</div>

				<!-- Reset Styles -->
				<div class="flex items-end">
					<button
						onclick={() => {
							captionStyle.fontSize = 18;
							captionStyle.color = '#EE8C36';
							captionStyle.backgroundColor = 'rgba(0, 0, 0, 0.8)';
							captionStyle.fontFamily = 'Arial';
							captionStyle.padding = 12;
						}}
						class="w-full bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600"
					>
						Reset Styles
					</button>
				</div>
			</div>
		</div>
	{/if}

	</div> <!-- End Left Column -->

	<!-- Right Column: Video Preview -->
	<div class="flex flex-col items-center justify-start">
		{#if audioUrl}
			<div class="sticky top-6">
				<h3 class="text-lg font-medium mb-4 text-center">Video Preview</h3>
				<div class="relative">
					<canvas
						bind:this={videoCanvas}
						class="border-4 border-orange-500 rounded-lg shadow-lg"
						style="width: 270px; height: 480px; background: #000; display: block;"
					></canvas>
					<div class="mt-2 text-sm text-gray-500 text-center">
						1080x1920
					</div>
				</div>
			</div>
		{:else}
			<div class="flex items-center justify-center h-96 border-2 border-dashed border-gray-300 rounded-lg">
				<div class="text-center text-gray-500">
					<div class="text-4xl mb-2">📹</div>
					<p>Video preview will appear here</p>
					<p class="text-sm">Generate a video to see the preview</p>
				</div>
			</div>
		{/if}
	</div>

	</div> <!-- End Main Grid -->
</div>
